using App.ECommerce.ProcessFlow.Interface;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Model;
using App.ECommerce.Repository.Entities;
using AutoMapper;
using Microsoft.Extensions.Logging;
using System.Net.Http;
using Newtonsoft.Json;
using System.Text;
using App.ECommerce.Resource.Dtos;
using App.ECommerce.Resource.Dtos.SapoOmniDtos;
using App.ECommerce.Units.Abstractions.Entities;
using App.ECommerce.Services.UploadStore;
using App.Base.Repository.Entities;
using App.ECommerce.Units.Enums;
using System.Text.Json.Nodes;

namespace App.ECommerce.ProcessFlow.Implement;

public class SapoOmniFlow : ISapoOmniFlow
{
    private readonly ISapoOmniConfigRepository _sapoOmniConfigRepository;
    private readonly IItemsRepository _itemsRepository;
    private readonly IOrderRepository _orderRepository;
    private readonly IMapper _mapper;
    private readonly ILogger<SapoOmniFlow> _logger;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ICategoryRepository _categoryRepository;
    private readonly IUserRepository _userRepository;
    private readonly IConfiguration _configuration;

    public SapoOmniFlow(
        ISapoOmniConfigRepository sapoOmniConfigRepository,
        IItemsRepository itemsRepository,
        IOrderRepository orderRepository,
        IMapper mapper,
        ILogger<SapoOmniFlow> logger,
        IHttpClientFactory httpClientFactory,
        ICategoryRepository categoryRepository,
        IUserRepository userRepository,
        IConfiguration configuration)
    {
        _sapoOmniConfigRepository = sapoOmniConfigRepository;
        _itemsRepository = itemsRepository;
        _orderRepository = orderRepository;
        _mapper = mapper;
        _logger = logger;
        _httpClientFactory = httpClientFactory;
        _categoryRepository = categoryRepository;
        _userRepository = userRepository;
        _configuration = configuration;
    }

    #region Config Management

    public async Task<bool> SaveConfigAsync(SapoOmniConfigDto dto)
    {
        try
        {
            var exists = await _sapoOmniConfigRepository.FindByDomainApiAndShopId(dto.DomainApi, dto.ShopId);
            if (exists != null)
            {
                _logger.LogWarning("SapoOmni config already exists for shop {ShopId} and domain {Domain}",
                    dto.ShopId, dto.DomainApi);
                return false;
            }

            var entity = new SapoOmniConfig();
            entity.Status = "Pending";
            entity.ShopId = dto.ShopId;
            entity.DomainApi = dto.DomainApi.Trim();
            entity.ClientId = dto.ClientId?.Trim() ?? "";
            entity.ClientSecret = dto.ClientSecret?.Trim() ?? "";
            entity.AccessToken = dto.AccessToken?.Trim() ?? "";
            entity.CreatedDate = DateTime.UtcNow;
            entity.LastSyncAt = DateTime.UtcNow;
            if (!string.IsNullOrEmpty(dto.AccessToken))
            {
                entity.AccessToken = dto.AccessToken.Trim();
                entity.Status = "Active";

                var tempResult = await _sapoOmniConfigRepository.CreateOrUpdate(entity);
                if (tempResult == null)
                {
                    return false;
                }

                var validateResult = await ValidateAccessTokenAsync(dto.ShopId);
                if (!validateResult.IsSuccess)
                {
                    _logger.LogWarning("Provided access token is invalid for shop {ShopId}: {Error}",
                        dto.ShopId, validateResult.Message);
                    entity.Status = "Invalid";
                }
            }
            else if (
                !string.IsNullOrEmpty(dto.ClientId) &&
                !string.IsNullOrEmpty(dto.ClientSecret))
            {
                _logger.LogInformation("Processing OAuth flow for shop {ShopId}", dto.ShopId);

                var tempResult = await _sapoOmniConfigRepository.CreateOrUpdate(entity);
                if (tempResult == null)
                {
                    return false;
                }

                // Thực hiện OAuth exchange
                var tokenResult = await ExchangeCodeForAccessTokenAsync(dto.ShopId, dto.AuthorizationCode, dto.ClientId, dto.ClientSecret);

                if (!tokenResult.IsSuccess)
                {
                    _logger.LogError("Failed to exchange authorization code for shop {ShopId}: {Error}",
                        dto.ShopId, tokenResult.Message);

                    await _sapoOmniConfigRepository.DeleteByDomainApiAndShopId(dto.ShopId, dto.DomainApi);
                    return false;
                }

                entity.AccessToken = tokenResult.Data;
                entity.Status = "Active";

                var validateResult = await ValidateAccessTokenAsync(dto.ShopId);
                if (!validateResult.IsSuccess)
                {
                    _logger.LogWarning("Access token validation failed for shop {ShopId}: {Error}",
                        dto.ShopId, validateResult.Message);
                    entity.Status = "Invalid";
                }
            }


            var result = await _sapoOmniConfigRepository.CreateOrUpdate(entity);

            if (result != null && entity.Status == "Active")
            {
                var webhookUrl = GetDefaultWebhookUrl();

                _logger.LogInformation("Creating webhooks for new SapoOmni config: Shop {ShopId}, Domain {Domain}",
                    dto.ShopId, dto.DomainApi);

                var webhookResult = await CreateWebhooksAsync(dto.ShopId, webhookUrl);
                if (!webhookResult.IsSuccess)
                {
                    _logger.LogWarning("Failed to create webhooks for shop {ShopId}: {Error}",
                        dto.ShopId, webhookResult.Message);
                }
                else
                {
                    _logger.LogInformation("Successfully created webhooks for shop {ShopId}", dto.ShopId);
                }

                _logger.LogInformation("Successfully created SapoOmni config for shop {ShopId} with status {Status}",
                    dto.ShopId, entity.Status);
            }

            return result != null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error saving SapoOmni config for shop {ShopId}", dto.ShopId);
            return false;
        }
    }

    public async Task<bool> UpdateConfigAsync(UpdateSapoOmniConfigDto dto)
    {
        try
        {
            var existingConfig = await _sapoOmniConfigRepository.GetByShopId(dto.ShopId);
            if (existingConfig == null)
            {
                return false;
            }

            var duplicateExists = await _sapoOmniConfigRepository.FindByDomainApiAndShopId(dto.DomainApi, dto.ShopId);
            if (duplicateExists != null && existingConfig.DomainApi != dto.DomainApi)
            {
                return false;
            }


            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating SapoOmni config for shop {ShopId}", dto.ShopId);
            return false;
        }
    }

    public async Task<bool> DeleteConfigAsync(string shopId, string domainApi)
    {
        try
        {
            // Xóa webhook trước khi xóa config
            _logger.LogInformation("Deleting webhooks for shop {ShopId} before removing config", shopId);

            var webhookResult = await DeleteWebhooksAsync(shopId);
            if (!webhookResult.IsSuccess)
            {
                _logger.LogWarning("Failed to delete webhooks for shop {ShopId}: {Error}",
                    shopId, webhookResult.Message);
            }

            var result = await _sapoOmniConfigRepository.DeleteByDomainApiAndShopId(shopId, domainApi);

            if (result)
            {
                _logger.LogInformation("Successfully deleted SapoOmni config and webhooks for shop {ShopId}", shopId);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting SapoOmni config for shop {ShopId}", shopId);
            return false;
        }
    }


    public async Task<SapoOmniConfigDto?> GetConfigByDomainApiAsync(string domainApi, string shopId)
    {
        try
        {
            var config = await _sapoOmniConfigRepository.FindByDomainApiAndShopId(domainApi, shopId);
            if (config == null) return null;

            return _mapper.Map<SapoOmniConfigDto>(config);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting SapoOmni config for domain: {DomainApi}", domainApi);
            return null;
        }
    }

    public async Task<SapoOmniConfigDto> GetConfigsByShopIdAsync(string shopId)
    {
        try
        {
            var config = await _sapoOmniConfigRepository.GetByShopId(shopId);
            var configDto = new SapoOmniConfigDto
            {
                ShopId = shopId,
                DomainApi = config?.DomainApi ?? "",
                AccessToken = config?.AccessToken ?? "",
                Status = config?.Status == "Active" ? TypeStatus.Actived : TypeStatus.InActived,
            };
            return configDto;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting SapoOmni configs for shop: {ShopId}", shopId);
            return new SapoOmniConfigDto();
        }
    }

    #endregion

    #region Webhook Handlers

    public async Task<Result<bool>> AddOrUpdateProductFromSapoOmniWebhook(SapoOmniProductDto productDto, string domainApi, string accessToken)
    {
        return await ExecuteWebhookOperation(
            async () =>
            {

                await ProcessProduct(productDto, domainApi);

                return true;
            },
            domainApi,
            $"product {productDto.Id}",
            "Product webhook processing error"
        );
    }

    public Task<Result<bool>> DeleteProductFromSapoOmniWebhook(string shopId, long productId)
    {
        try
        {
            _logger.LogInformation("Processing product deletion webhook for product ID: {ProductId}", productId);

            var product = _itemsRepository.FindByExternalId(shopId, productId.ToString(), SyncServiceEnum.Sapo);
            if (product != null)
            {
                _itemsRepository.DeleteItems(product.ItemsId);
            }

            return Task.FromResult(Result<bool>.Success(true));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting SapoOmni product ID: {ProductId}", productId);
            return Task.FromResult(Result<bool>.Failure("Product deletion error"));
        }
    }

    public async Task<Result<bool>> AddOrUpdateOrderFromSapoOmniWebhook(SapoOmniOrderDto orderDto, string domainApi, string accessToken)
    {
        return await ExecuteWebhookOperation(
            async () =>
            {

                await ProcessOrder(orderDto, domainApi);

                return true;
            },
            domainApi,
            $"order {orderDto.Id}",
            "Order webhook processing error"
        );
    }

    public Task<Result<bool>> DeleteOrderFromSapoOmniWebhook(string shopId, long orderId, string domainApi)
    {
        try
        {
            _logger.LogInformation("Processing order deletion webhook for order ID: {OrderId}", orderId);

            var orderItems = _orderRepository.FindByExternalId(shopId, orderId.ToString(), SyncServiceEnum.Sapo);
            if (orderItems != null)
            {
                _orderRepository.DeleteOrder(orderItems.Id);
            }

            return Task.FromResult(Result<bool>.Success(true));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting SapoOmni order ID: {OrderId}", orderId);
            return Task.FromResult(Result<bool>.Failure("Order deletion error"));
        }
    }

    public Task<Result<bool>> UpdateInventoryFromSapoOmniWebhook(SapoOmniInventoryWebhookDto inventoryDto, string domainApi, string accessToken)
    {
        try
        {
            _logger.LogInformation("Processing inventory webhook for domain: {DomainApi}, variant ID: {VariantId}", domainApi, inventoryDto.VariantId);
            return Task.FromResult(Result<bool>.Success(true));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing SapoOmni inventory webhook for domain: {DomainApi}", domainApi);
            return Task.FromResult(Result<bool>.Failure("Inventory webhook processing error"));
        }
    }

    public async Task<Result<bool>> AddOrUpdateCustomerFromSapoOmniWebhook(SapoOmniCustomerDto customerDto, string domainApi, string accessToken)
    {
        return await ExecuteWebhookOperation(
            async () =>
            {

                await ProcessCustomer(customerDto, domainApi);
                return true;
            },
            domainApi,
            $"customer {customerDto.Id}",
            "Customer webhook processing error"
        );
    }

    #endregion

    #region Helper Methods

    private async Task<Result<bool>> ExecuteWebhookOperation(
        Func<Task<bool>> operation,
        string domainApi,
        string entityInfo,
        string errorMessage)
    {
        try
        {
            _logger.LogInformation("Processing {EntityInfo} webhook for domain: {DomainApi}", entityInfo, domainApi);

            await operation();
            await _sapoOmniConfigRepository.UpdateLastSyncAt(domainApi, "", DateTime.UtcNow);

            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing SapoOmni {EntityInfo} webhook for domain: {DomainApi}", entityInfo, domainApi);
            return Result<bool>.Failure(errorMessage);
        }
    }

    private async Task ProcessProduct(SapoOmniProductDto product, string domainApi)
    {
        try
        {
            var config = await _sapoOmniConfigRepository.FindByDomainApi(domainApi);
            if (config == null) return;

            string categoryId = await EnsureCategoryExists(product, config.ShopId);
            var existingItems = _itemsRepository.FindByExternalIds(product.Id.ToString(), SyncServiceEnum.Sapo);

            bool isVariant = product.Variants != null && product.Variants.Count > 1;

            if (existingItems != null)
            {
                foreach (var item in existingItems)
                {
                    _itemsRepository.DeleteItems(item.ItemsId);

                }
            }

            // Create new product/variants
            if (!isVariant)
            {
                var firstVariant = product.Variants?.FirstOrDefault();
                var newItem = MapSapoProductToItems(product, firstVariant, config.ShopId, categoryId);
                _itemsRepository.CreateItems(newItem);
            }
            else
            {
                foreach (var variant in product.Variants)
                {
                    var newItem = MapSapoProductToItems(product, variant, config.ShopId, categoryId);
                    _itemsRepository.CreateItems(newItem);
                }
            }

            _logger.LogDebug("Processed product {ProductId} for domain {DomainApi}", product.Id, domainApi);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing product {ProductId}", product.Id);
        }
    }

    private Items MapSapoProductToItems(SapoOmniProductDto sapoProduct, SapoOmniVariantDto? variant, string shopId, string categoryId)
    {
        bool isVariant = sapoProduct.Variants != null && sapoProduct.Variants.Count > 1;

        var item = new Items
        {
            ItemsName = sapoProduct.Name ?? "",
            ItemsCode = sapoProduct.Id.ToString(),
            ExternalSource = SyncServiceEnum.Sapo,
            CategoryIds = new List<string> { categoryId },
            ItemsInfo = sapoProduct.Description ?? "",
            ItemsType = TypeItems.Product,
            ShopId = shopId,
            ExternalId = sapoProduct.Id.ToString(),
            IsTop = false,
            IsShow = sapoProduct.Status == "active",
            IsVariant = isVariant,
            TypePublish = sapoProduct.Status == "active" ? TypePublish.Publish : TypePublish.UnPublish,
            Status = TypeStatus.Actived,
            Created = sapoProduct.CreatedOn,
            Updated = sapoProduct.ModifiedOn,
            SeoTags = MapSeoTags(sapoProduct.Tags),
            PartnerId = "",
            WarehouseId = "",
            Images = sapoProduct.Images?.Select(img => new MediaInfo
            {
                Link = img.FullPath ?? "",
                Type = TypeMedia.IMAGE
            }).ToList() ?? new List<MediaInfo>(),
        };

        if (variant != null)
        {
            item.PriceReal = (long)(variant.GetPriceByCode("BANBUON") ?? 0); // Giá bán buôn
            item.Price = (long)(variant.GetPriceByCode("BANLE") ?? 0); // Giá bán lẻ
            item.PriceCapital = (long)(variant.GetPriceByCode("GIANHAP") ?? 0); // Giá nhập
            item.Quantity = variant.InventoryQuantity ?? 0;
            item.ItemsWeight = (double)(variant.WeightValue);
        }

        if (isVariant && variant != null)
        {
            item.VariantNameOne = sapoProduct.Opt1;
            item.VariantValueOne = variant.Opt1;
            item.VariantNameTwo = sapoProduct.Opt2;
            item.VariantValueTwo = variant.Opt2;
            item.VariantNameThree = sapoProduct.Opt3;
            item.VariantValueThree = variant.Opt3;
            // Note: Sapo API for product webhook doesn't seem to provide variant-specific image.
            // Using main product images for all variants.
        }

        return item;
    }

    private ProductDto MapSapoOmniProductToProductDto(SapoOmniProductDto sapoProduct, string shopId, string categoryId)
    {
        // Lấy variant đầu tiên nếu có
        var firstVariant = sapoProduct.Variants?.FirstOrDefault();

        return new ProductDto
        {
            ItemsId = sapoProduct.Id.ToString(),
            ItemsName = sapoProduct.Name ?? "",
            ItemsCode = firstVariant?.Sku ?? sapoProduct.Id.ToString(),
            ExternalSource = SyncServiceEnum.Sapo,
            CategoryIds = new List<string> { categoryId },

            // Mapping giá theo price_list.code
            PriceReal = (long?)(firstVariant?.GetPriceByCode("BANBUON") ?? 0), // Giá bán buôn
            Price = (long?)(firstVariant?.GetPriceByCode("BANLE") ?? 0), // Giá bán lẻ
            PriceCapital = (long?)(firstVariant?.GetPriceByCode("GIANHAP") ?? 0), // Giá nhập

            Quantity = firstVariant?.InventoryQuantity ?? 0,

            Images = sapoProduct.Images?.Select(img => new MediaInfo
            {
                Link = img.FullPath ?? "",
                Type = TypeMedia.IMAGE
            }).ToList() ?? new List<MediaInfo>(),

            ItemsInfo = sapoProduct.Description ?? "",
            ItemsType = TypeItems.Product,
            ShopId = shopId,
            ItemsWeight = (double?)(firstVariant?.WeightValue ?? 0),
            ExternalId = sapoProduct.Id.ToString(),
            IsTop = false,
            IsShow = sapoProduct.Status == "active",
            IsVariant = sapoProduct.Variants != null && sapoProduct.Variants.Count > 1,
            ListVariant = MapSapoOmniVariants(sapoProduct),

            TypePublish = sapoProduct.Status == "active" ? TypePublish.Publish : TypePublish.UnPublish,
            Status = TypeStatus.Actived,

            Created = sapoProduct.CreatedOn,
            Updated = sapoProduct.ModifiedOn,

            SeoTags = MapSeoTags(sapoProduct.Tags),

            PartnerId = "",
            WarehouseId = "",
        };
    }

    private List<VariantBase> MapSapoOmniVariants(SapoOmniProductDto sapoProduct)
    {
        var variants = new List<VariantBase>();

        if (sapoProduct.Variants != null && sapoProduct.Variants.Count > 0)
        {
            foreach (var variant in sapoProduct.Variants)
            {
                var variantBase = new VariantBase
                {
                    ItemsId = variant.Id.ToString(),
                    VariantNameOne = sapoProduct.Opt1,
                    VariantValueOne = variant.Opt1,
                    VariantNameTwo = sapoProduct.Opt2,
                    VariantValueTwo = variant.Opt2,
                    VariantNameThree = sapoProduct.Opt3,
                    VariantValueThree = variant.Opt3,
                    PriceReal = (long?)(variant.GetPriceByCode("BANBUON") ?? 0), // Giá bán buôn
                    Price = (long?)(variant.GetPriceByCode("BANLE") ?? 0), // Giá bán lẻ
                    PriceCapital = (long?)(variant.GetPriceByCode("GIANHAP") ?? 0), // Giá nhập

                    Quantity = variant.InventoryQuantity ?? 0,

                };
                variants.Add(variantBase);
            }
        }

        return variants;
    }

    private List<SeoTag> MapSeoTags(string? tags)
    {
        var seoTags = new List<SeoTag>();

        if (!string.IsNullOrEmpty(tags))
        {
            var tagArray = tags.Split(',', StringSplitOptions.RemoveEmptyEntries);
            foreach (var tag in tagArray)
            {
                seoTags.Add(new SeoTag
                {
                    Tags = tag.Trim(),
                });
            }
        }

        return seoTags;
    }

    private async Task<string> EnsureCategoryExists(SapoOmniProductDto product, string shopId)
    {
        try
        {
            // Sử dụng Category từ SapoOmni
            string categoryName = !string.IsNullOrEmpty(product.Category)
                ? product.Category
                : (!string.IsNullOrEmpty(product.ProductType) ? product.ProductType : "Sản phẩm SapoOmni");

            var category = await _categoryRepository.FindByCategoryName(categoryName);

            if (category != null)
            {
                return category.CategoryId;
            }
            else
            {
                // Tạo category mới
                var newCategory = new Category
                {
                    CategoryId = Guid.NewGuid().ToString(),
                    CategoryName = categoryName,
                    ShopId = shopId,
                    CategoryType = TypeCategory.Product,
                    CategoryLevel = "1",
                    Publish = TypeCategoryPublish.Publish,
                    Created = DateTime.UtcNow,
                    Updated = DateTime.UtcNow
                };
                var created = await _categoryRepository.CreateCategory(newCategory);
                return created.CategoryId;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error ensuring category exists for product {ProductId}", product.Id);
            return string.Empty;
        }
    }

    private async Task ProcessOrder(SapoOmniOrderDto order, string domainApi)
    {
        try
        {
            var config = await _sapoOmniConfigRepository.FindByDomainApi(domainApi);
            if (config == null)
            {
                _logger.LogWarning("Config not found for domain {DomainApi}", domainApi);
                return;
            }

            User? user = null;
            if (order.CustomerData != null)
            {
                user = await EnsureUserFromSapoOmniCustomer(order.CustomerData, config.ShopId);
            }

            var existingOrder = _orderRepository.FindByExternalId(config.ShopId, order.Id.ToString(), SyncServiceEnum.Sapo);
            if (existingOrder != null && existingOrder.ShopId != config.ShopId)
            {
                existingOrder = null;
            }

            var listItems = MapOrderLineItems(config.ShopId, order.OrderLineItems);
            var shippingAddress = MapShippingAddress(order);

            if (existingOrder != null)
            {
                UpdateExistingOrder(existingOrder, order, listItems, shippingAddress);
            }
            else
            {
                await CreateNewOrder(order, config.ShopId, listItems, shippingAddress);
            }

            _logger.LogDebug("Processed order {OrderCode} for domain {DomainApi}", order.Code, domainApi);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing order {OrderId}", order.Id);
        }
    }

    private List<ItemsOrder> MapOrderLineItems(string shopId, List<SapoOmniOrderLineItemDto>? orderLineItems)
    {
        var listItems = new List<ItemsOrder>();

        foreach (var lineItem in orderLineItems ?? new List<SapoOmniOrderLineItemDto>())
        {
            var items = _itemsRepository.FindByExternalId(shopId, lineItem.Id.ToString(), SyncServiceEnum.Sapo);
            var existingItem = items;

            var itemOrder = new ItemsOrder
            {
                ItemsCode = lineItem.Sku,
                ItemsName = existingItem?.ItemsName ?? lineItem.ProductName,
                ItemsId = existingItem?.ItemsId,
                Quantity = (int)lineItem.Quantity,
                Price = (long)lineItem.Price,
                Note = lineItem.Note ?? "",
                VoucherDiscount = (long)lineItem.DiscountRate,
                TaxAmount = (long)lineItem.TaxAmount,
                ExternalId = lineItem.Id.ToString(),
                ExternalSource = SyncServiceEnum.Sapo,
            };
            listItems.Add(itemOrder);
        }

        return listItems;
    }

    private ShippingAddress MapShippingAddress(SapoOmniOrderDto order)
    {
        return new ShippingAddress
        {
            FullName = order.ShippingAddress?.FullName ?? order.CustomerData?.Name ?? "",
            PhoneNumber = order.ShippingAddress?.PhoneNumber ?? order.PhoneNumber ?? "",
            Address = order.ShippingAddress?.Address1 ?? "",
            WardName = order.ShippingAddress?.Ward ?? "",
            DistrictName = order.ShippingAddress?.District ?? "",
            ProvinceName = order.ShippingAddress?.City ?? "",
        };
    }

    private void UpdateExistingOrder(Order existingOrder, SapoOmniOrderDto order, List<ItemsOrder> listItems, ShippingAddress shippingAddress)
    {
        existingOrder.ListItems = listItems;
        existingOrder.Price = (long)order.Total;
        existingOrder.VoucherPromotionPrice = (long)order.TotalDiscount;
        existingOrder.TotalTaxAmount = (long)order.TotalTax;
        existingOrder.Notes = order.Note ?? "";
        existingOrder.UserShippingAddress = shippingAddress;
        existingOrder.TransportPrice = (long)(order.DeliveryFee ?? 0);
        existingOrder.Updated = order.ModifiedOn;
        MapSapoStatusesToInternalOrder(order, existingOrder);

        _orderRepository.UpdateOrder(existingOrder);
    }

    private async Task CreateNewOrder(SapoOmniOrderDto order, string shopId, List<ItemsOrder> listItems, ShippingAddress shippingAddress)
    {
        var newOrder = new Order
        {
            OrderId = Guid.NewGuid().ToString(),
            OrderNo = order.Code,
            ListItems = listItems,
            Price = (long)order.Total,
            VoucherPromotionPrice = (long)order.TotalDiscount,
            TotalTaxAmount = (long)order.TotalTax,
            Notes = order.Note ?? "",
            OrderOrigin = TypeOrigin.SapoOmni,
            UserShippingAddress = shippingAddress,
            ShopId = shopId,
            Status = TypeStatus.Actived,
            Created = order.CreatedOn,
            Updated = order.ModifiedOn,
            ExternalId = order.Id.ToString(),
            ExternalSource = SyncServiceEnum.Sapo,
            TransportPrice = (long)(order.DeliveryFee ?? 0),
        };
        MapSapoStatusesToInternalOrder(order, newOrder);


        await _orderRepository.CreateOrder(newOrder);
    }

    private Task<User?> EnsureUserFromSapoOmniCustomer(SapoOmniCustomerDto customer, string shopId)
    {
        try
        {
            string phoneNumber = customer.PhoneNumber;
            string email = customer.Email ?? "";

            User? user = FindExistingUser(phoneNumber, email, shopId, customer.Name);

            if (user == null)
            {
                user = CreateNewUser(customer, shopId);
            }
            else
            {
                UpdateExistingUser(user, customer);
            }

            return Task.FromResult<User?>(user);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error ensuring user from SapoOmni customer {CustomerId}", customer.Id);
            return Task.FromResult<User?>(null);
        }
    }

    private User? FindExistingUser(string phoneNumber, string email, string shopId, string name)
    {
        User? user = null;

        if (!string.IsNullOrEmpty(phoneNumber))
        {
            user = _userRepository.FindByUserPhone(shopId, phoneNumber);
        }

        if (user == null && !string.IsNullOrEmpty(email))
        {
            user = _userRepository.FindByUserEmail(shopId, email);
        }
        if (user == null && !string.IsNullOrEmpty(name))
        {
            user = _userRepository.FindByUsername(shopId, email);
        }

        return user;
    }

    private User CreateNewUser(SapoOmniCustomerDto customer, string shopId)
    {
        var mainAddress = customer.Addresses?.FirstOrDefault();

        var user = new User
        {
            UserId = Guid.NewGuid().ToString(),
            ShopId = shopId,
            Email = customer.Email ?? "",
            PhoneNumber = customer.PhoneNumber,
            Fullname = customer.Name ?? "",
            Username = customer.Name ?? "",
            Address = GetCustomerMainAddress(customer),
            WardName = mainAddress?.Ward ?? "",
            DistrictName = mainAddress?.District ?? "",
            ProvinceName = mainAddress?.City ?? "",
            Status = TypeStatus.Actived,
            Created = customer.CreatedOn,
            Updated = customer.ModifiedOn,
        };
        _userRepository.CreateUser(user);
        return user;
    }

    private void UpdateExistingUser(User user, SapoOmniCustomerDto customer)
    {
        var mainAddress = customer.Addresses?.FirstOrDefault();

        user.Email = customer.Email ?? "";
        user.PhoneNumber = customer.PhoneNumber;
        user.Username = customer.Name ?? user.Username;
        user.Address = GetCustomerMainAddress(customer) ?? user.Address;
        user.Updated = customer.ModifiedOn;
        if (mainAddress != null)
        {
            user.WardName = mainAddress.Ward ?? user.WardName;
            user.DistrictName = mainAddress.District ?? user.DistrictName;
            user.ProvinceName = mainAddress.City ?? user.ProvinceName;
        }
        _userRepository.UpdateUser(user);
    }

    private async Task ProcessCustomer(SapoOmniCustomerDto customer, string domainApi)
    {
        try
        {
            var config = await _sapoOmniConfigRepository.FindByDomainApi(domainApi);
            if (config == null)
            {
                _logger.LogWarning("Config not found for domain {DomainApi}", domainApi);
                return;
            }

            await EnsureUserFromSapoOmniCustomer(customer, config.ShopId);
            _logger.LogDebug("Processed customer {CustomerId} for domain {DomainApi}", customer.Id, domainApi);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing customer {CustomerId}", customer.Id);
        }
    }

    private string? GetCustomerMainAddress(SapoOmniCustomerDto customer)
    {
        if (customer.Addresses != null && customer.Addresses.Count > 0)
        {
            var mainAddress = customer.Addresses.First();
            return BuildFullAddress(mainAddress);
        }
        return null;
    }


    private void MapSapoStatusesToInternalOrder(SapoOmniOrderDto sapoOrder, Order internalOrder)
    {
        // 1. Ánh xạ trạng thái thanh toán (Sapo payment_status -> StatusPay)
        switch (sapoOrder.PaymentStatus)
        {
            case "paid":
                internalOrder.StatusPay = TypePayStatus.Paid;
                break;
            case "refunded":
            case "partially_refunded":
                internalOrder.StatusPay = TypePayStatus.Refund;
                break;
            case "pending":
            case "partially_paid":
            case "voided":
            default:
                internalOrder.StatusPay = TypePayStatus.NotPaid;
                break;
        }

        switch (sapoOrder.FulfillmentStatus)
        {
            case "fulfilled":
                internalOrder.StatusTransport = TypeTransportStatus.Success;
                break;
            case "shipped":
                internalOrder.StatusTransport = TypeTransportStatus.Transporting;
                break;
            case "partially_fulfilled":
                internalOrder.StatusTransport = TypeTransportStatus.Delivering;
                break;
            case "returned":
                internalOrder.StatusTransport = TypeTransportStatus.Refunded;
                break;
            case "unshipped":
            case "not_fulfilled":
            default:
                internalOrder.StatusTransport = TypeTransportStatus.WaitingForDelivery;
                break;
        }

        switch (sapoOrder.Status)
        {
            case "cancelled":
                internalOrder.StatusOrder = TypeOrderStatus.Failed;
                internalOrder.StatusTransport = TypeTransportStatus.Cancel;
                break;
            case "closed":
                internalOrder.StatusOrder = TypeOrderStatus.Success;
                break;
            case "draft":
                internalOrder.StatusOrder = TypeOrderStatus.Pending;
                break;
            case "open":
            case "finalized":
            default:
                if (internalOrder.StatusPay == TypePayStatus.Paid)
                {
                    internalOrder.StatusOrder = TypeOrderStatus.Paid;
                }
                else
                {
                    internalOrder.StatusOrder = TypeOrderStatus.Pending;
                }
                break;
        }

        if (sapoOrder.FulfillmentStatus == "fulfilled" && sapoOrder.PaymentStatus == "paid")
        {
            internalOrder.StatusOrder = TypeOrderStatus.Success;
            internalOrder.StatusTransport = TypeTransportStatus.Success;
        }
    }

    #endregion

    #region Address Helper Methods

    private string BuildFullAddress(SapoOmniAddressDto? address)
    {
        if (address == null) return "";

        var addressParts = new List<string?>
        {
            address.Address1,
            address.Address2,

        };

        return string.Join(", ", addressParts.Where(part => !string.IsNullOrWhiteSpace(part)));
    }

    #endregion

    #region Access Token and Config Status Management

    public async Task<bool> UpdateAccessTokenAsync(string domainApi, string shopId, string accessToken)
    {
        try
        {
            await _sapoOmniConfigRepository.UpdateAccessToken(domainApi, shopId, accessToken);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating access token for domain: {DomainApi}", domainApi);
            return false;
        }
    }

    public async Task<bool> UpdateConfigStatusAsync(string domainApi, string shopId, string status)
    {
        try
        {
            await _sapoOmniConfigRepository.UpdateStatus(domainApi, shopId, status);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating status for domain: {DomainApi}", domainApi);
            return false;
        }
    }

    #endregion

    #region Sync To SapoOmni Operations

    public async Task<Result<string>> CreateOrderToSapoOmniAsync(Order order, string shopId)
    {
        return await ExecuteSapoOmniApiOperation<string>(
            async (config, httpClient) =>
            {
                var sapoOrder = MapOrderToSapoOmniOrderDto(order);
                var payload = new { order = sapoOrder };
                var apiUrl = $"{config.DomainApi}/admin/orders.json";

                var response = await httpClient.PostAsync(apiUrl, CreateJsonContent(payload));
                var json = await response.Content.ReadAsStringAsync();

                if (!response.IsSuccessStatusCode)
                {
                    _logger.LogError("Failed to create order in SapoOmni: {StatusCode} - {Response}", response.StatusCode, json);
                    return Result<string>.Failure($"Gửi đơn sang SapoOmni thất bại: {response.StatusCode}");
                }

                var responseObj = JsonConvert.DeserializeObject<dynamic>(json);
                var sapoOrderId = responseObj?.order?.id?.ToString();

                if (!string.IsNullOrEmpty(sapoOrderId))
                {
                    order.OrderOrigin = TypeOrigin.SapoOmni;
                    _orderRepository.UpdateOrder(order);
                }

                return Result<string>.Success(sapoOrderId ?? "");
            },
            shopId,
            "Có lỗi xảy ra khi tạo đơn hàng trên SapoOmni"
        );
    }

    public async Task<Result<bool>> UpdateOrderToSapoOmniAsync(Order order, string shopId)
    {
        return await ExecuteSapoOmniApiOperation(
            async (config, httpClient) =>
            {
                if (string.IsNullOrEmpty(order.ThirdPartyOrderId) && order.OrderOrigin != TypeOrigin.SapoOmni)
                    return Result<bool>.Failure("Không tìm thấy ID đơn hàng SapoOmni để cập nhật");

                var sapoOrder = MapOrderToSapoOmniOrderDto(order);
                var payload = new { order = sapoOrder };
                var apiUrl = $"{config.DomainApi}/admin/orders/{order.ThirdPartyOrderId}.json";

                var response = await httpClient.PutAsync(apiUrl, CreateJsonContent(payload));

                if (!response.IsSuccessStatusCode)
                {
                    var json = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Failed to update order in SapoOmni: {StatusCode} - {Response}", response.StatusCode, json);
                    return Result<bool>.Failure($"Cập nhật đơn sang SapoOmni thất bại: {response.StatusCode}");
                }

                return Result<bool>.Success(true);
            },
            shopId,
            "Có lỗi xảy ra khi cập nhật đơn hàng trên SapoOmni"
        );
    }

    public async Task<Result<bool>> CreateProductToSapoOmniAsync(Items product, string shopId)
    {
        return await ExecuteSapoOmniApiOperation<bool>(
            async (config, httpClient) =>
            {
                var sapoProduct = MapItemsToSapoOmniProductDto(product);
                var payload = new { product = sapoProduct };
                var apiUrl = $"{config.DomainApi}/admin/products.json";

                var response = await httpClient.PostAsync(apiUrl, CreateJsonContent(payload));

                if (!response.IsSuccessStatusCode)
                {
                    var json = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Failed to create product in SapoOmni: {StatusCode} - {Response}", response.StatusCode, json);
                    return Result<bool>.Failure($"Tạo sản phẩm trên SapoOmni thất bại: {response.StatusCode}");
                }

                product.ExternalSource = SyncServiceEnum.Sapo;
                _itemsRepository.UpdateItems(product);

                return Result<bool>.Success(true);
            },
            shopId,
            "Có lỗi xảy ra khi tạo sản phẩm trên SapoOmni"
        );
    }

    public async Task<Result<bool>> UpdateProductToSapoOmniAsync(Items product, string shopId)
    {
        return await ExecuteSapoOmniApiOperation<bool>(
            async (config, httpClient) =>
            {
                var sapoProductId = await FindSapoOmniProductIdByCode(product.ItemsCode, config);
                if (string.IsNullOrEmpty(sapoProductId))
                    return Result<bool>.Failure("Không tìm thấy sản phẩm trên SapoOmni để cập nhật");

                var sapoProduct = MapItemsToSapoOmniProductDto(product);
                var payload = new { product = sapoProduct };
                var apiUrl = $"{config.DomainApi}/admin/products/{sapoProductId}.json";

                var response = await httpClient.PutAsync(apiUrl, CreateJsonContent(payload));

                if (!response.IsSuccessStatusCode)
                {
                    var json = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Failed to update product in SapoOmni: {StatusCode} - {Response}", response.StatusCode, json);
                    return Result<bool>.Failure($"Cập nhật sản phẩm trên SapoOmni thất bại: {response.StatusCode}");
                }

                return Result<bool>.Success(true);
            },
            shopId,
            "Có lỗi xảy ra khi cập nhật sản phẩm trên SapoOmni"
        );
    }

    public async Task<Result<bool>> CreateCustomerToSapoOmniAsync(User customer, string shopId)
    {
        return await ExecuteSapoOmniApiOperation(
            async (config, httpClient) =>
            {
                var sapoCustomer = MapUserToSapoOmniCustomerDto(customer);
                var payload = new { customer = sapoCustomer };
                var apiUrl = $"{config.DomainApi}/admin/customers.json";

                var response = await httpClient.PostAsync(apiUrl, CreateJsonContent(payload));

                if (!response.IsSuccessStatusCode)
                {
                    var json = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Failed to create customer in SapoOmni: {StatusCode} - {Response}", response.StatusCode, json);
                    return Result<bool>.Failure($"Tạo khách hàng trên SapoOmni thất bại: {response.StatusCode}");
                }

                var responseObj = JsonConvert.DeserializeObject<dynamic>(await response.Content.ReadAsStringAsync());
                var sapoCustomerId = responseObj?.customer?.id?.ToString();

                if (!string.IsNullOrEmpty(sapoCustomerId))
                {
                    customer.ReferralCode = sapoCustomerId;
                    _userRepository.UpdateUser(customer);
                }

                return Result<bool>.Success(true);
            },
            shopId,
            "Có lỗi xảy ra khi tạo khách hàng trên SapoOmni"
        );
    }

    public async Task<Result<bool>> UpdateCustomerToSapoOmniAsync(User customer, string shopId)
    {
        return await ExecuteSapoOmniApiOperation(
            async (config, httpClient) =>
            {
                if (string.IsNullOrEmpty(customer.ReferrerCode))
                    return Result<bool>.Failure("Không tìm thấy ID khách hàng SapoOmni để cập nhật");

                var sapoCustomer = MapUserToSapoOmniCustomerDto(customer);
                var payload = new { customer = sapoCustomer };
                var apiUrl = $"{config.DomainApi}/admin/customers/{customer.ReferrerCode}.json";

                var response = await httpClient.PutAsync(apiUrl, CreateJsonContent(payload));

                if (!response.IsSuccessStatusCode)
                {
                    var json = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Failed to update customer in SapoOmni: {StatusCode} - {Response}", response.StatusCode, json);
                    return Result<bool>.Failure($"Cập nhật khách hàng trên SapoOmni thất bại: {response.StatusCode}");
                }

                return Result<bool>.Success(true);
            },
            shopId,
            "Có lỗi xảy ra khi cập nhật khách hàng trên SapoOmni"
        );
    }

    private async Task<Result<T>> ExecuteSapoOmniApiOperation<T>(
        Func<SapoOmniConfig, HttpClient, Task<Result<T>>> operation,
        string shopId,
        string errorMessage)
    {
        try
        {
            var config = await _sapoOmniConfigRepository.GetByShopId(shopId);
            if (config == null || string.IsNullOrEmpty(config.AccessToken))
                return Result<T>.Failure("Shop chưa cấu hình SapoOmni");

            using var httpClient = CreateConfiguredHttpClient(config.AccessToken);
            return await operation(config, httpClient);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error executing SapoOmni API operation for shop {ShopId}", shopId);
            return Result<T>.Failure(errorMessage);
        }
    }

    private HttpClient CreateConfiguredHttpClient(string accessToken)
    {
        var httpClient = _httpClientFactory.CreateClient();
        httpClient.DefaultRequestHeaders.Add("X-Sapo-Access-Token", accessToken);
        httpClient.DefaultRequestHeaders.Add("Accept", "application/json");
        httpClient.DefaultRequestHeaders.Add("Content-Type", "application/json");
        return httpClient;
    }

    private StringContent CreateJsonContent(object payload)
    {
        var jsonContent = JsonConvert.SerializeObject(payload);
        return new StringContent(jsonContent, Encoding.UTF8, "application/json");
    }

    #endregion

    #region Mapping Methods

    private object MapOrderToSapoOmniOrderDto(Order order)
    {
        return new
        {
            code = order.OrderNo,
            note = order.Notes,
            customer = new
            {
                name = order.UserShippingAddress?.FullName,
                phone_number = order.UserShippingAddress?.PhoneNumber,
            },
            billing_address = new
            {
                full_name = order.UserShippingAddress?.FullName,
                phone_number = order.UserShippingAddress?.PhoneNumber,
                address1 = order.UserShippingAddress?.Address,
                ward = order.UserShippingAddress?.WardName,
                district = order.UserShippingAddress?.DistrictName,
                city = order.UserShippingAddress?.ProvinceName,
                country = "Việt Nam"
            },
            shipping_address = new
            {
                full_name = order.UserShippingAddress?.FullName,
                phone_number = order.UserShippingAddress?.PhoneNumber,
                address1 = order.UserShippingAddress?.Address,
                ward = order.UserShippingAddress?.WardName,
                district = order.UserShippingAddress?.DistrictName,
                city = order.UserShippingAddress?.ProvinceName,
                country = "Việt Nam"
            },
            order_line_items = order.ListItems?.Select(item => new
            {
                sku = item.ItemsCode,
                product_name = item.ItemsName,
                quantity = item.Quantity,
                price = item.Price,
                note = item.Note
            }).ToList(),
            delivery_fee = order.TransportPrice,
            status = MapOrderStatusToSapoOmni(order.StatusOrder)
        };
    }

    private object MapItemsToSapoOmniProductDto(Items product)
    {
        return new
        {
            name = product.ItemsName,
            description = product.ItemsInfo,
            product_type = "normal",
            status = product.Status == TypeStatus.Actived ? "active" : "inactive",
            variants = new[]
            {
                new
                {
                    sku = product.ItemsCode,
                    barcode = product.ItemsCode,
                    inventory_quantity = product.Quantity,
                    weight_value = product.ItemsWeight,
                    price_lists = new[]
                    {
                        new { code = "BANLE", price = product.PriceReal },
                        new { code = "BANBUON", price = product.Price },
                        new { code = "GIANHAP", price = product.PriceCapital }
                    }
                }
            },
            images = product.Images?.Select(img => new
            {
                src = img.Link,
                alt = product.ItemsName
            }).ToList()
        };
    }

    private object MapUserToSapoOmniCustomerDto(User customer)
    {
        return new
        {
            name = customer.Username,
            email = customer.Email,
            phone_number = customer.PhoneNumber,
            description = customer.Notes,
            addresses = new[]
            {
                new
                {
                    full_name = customer.Username,
                    phone_number = customer.PhoneNumber,
                    address1 = customer.Address,
                    country = "Việt Nam",
                    status = "active"
                }
            }
        };
    }

    private string MapOrderStatusToSapoOmni(TypeOrderStatus status)
    {
        return status switch
        {
            TypeOrderStatus.Pending => "draft",
            TypeOrderStatus.Verified => "open",
            TypeOrderStatus.Paid => "finalized",
            TypeOrderStatus.Success => "closed",
            TypeOrderStatus.Failed => "cancelled",
            TypeOrderStatus.Refund => "cancelled",
            _ => "draft"
        };
    }

    private async Task<string?> FindSapoOmniProductIdByCode(string productCode, SapoOmniConfig config)
    {
        try
        {
            using var httpClient = CreateConfiguredHttpClient(config.AccessToken);
            var apiUrl = $"{config.DomainApi}/admin/products.json?sku={productCode}&limit=1";
            var response = await httpClient.GetAsync(apiUrl);

            if (!response.IsSuccessStatusCode) return null;

            var json = await response.Content.ReadAsStringAsync();
            var responseObj = JsonConvert.DeserializeObject<dynamic>(json);

            var products = responseObj?.products;
            if (products != null && products.Count > 0)
            {
                return products[0]?.id?.ToString();
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error finding SapoOmni product by code {ProductCode}", productCode);
            return null;
        }
    }


    #endregion

    #region OAuth Authentication

    public async Task<Result<string>> GetAuthorizationUrlAsync(string shopId, string clientId, string redirectUri, string state = "", List<string>? requestedScopes = null)
    {
        try
        {
            var config = await _sapoOmniConfigRepository.GetByShopId(shopId);
            if (config == null || string.IsNullOrEmpty(config.DomainApi))
            {
                return Result<string>.Failure("Chưa cấu hình domain API cho shop");
            }

            // Danh sách scope mặc định hoặc sử dụng scope được yêu cầu
            var defaultScopes = new List<string>
            {
                "read_products", "write_products",
                "read_customers", "write_customers",
                "read_suppliers", "write_suppliers",
                "read_orders", "write_orders",
                "read_fulfillments", "write_fulfillments",
                "read_order_returns", "write_order_returns",
                "read_stock_adjustments", "write_stock_adjustments",
                "read_stock_transfers", "write_stock_transfers",
                "read_purchase_orders", "write_purchase_orders"
            };

            var scopes = requestedScopes?.Any() == true ? requestedScopes : defaultScopes;
            var scopeString = string.Join(",", scopes);

            var authUrl = $"{config.DomainApi}/admin/oauth/authorize" +
                         $"?client_id={Uri.EscapeDataString(clientId)}" +
                         $"&redirect_uri={Uri.EscapeDataString(redirectUri)}" +
                         $"&scope={Uri.EscapeDataString(scopeString)}" +
                         $"&response_type=code" +
                         $"&state={Uri.EscapeDataString(state)}";

            _logger.LogInformation("Generated authorization URL for shop {ShopId} with scopes: {Scopes}",
                shopId, scopeString);
            return Result<string>.Success(authUrl);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating authorization URL for shop {ShopId}", shopId);
            return Result<string>.Failure("Có lỗi xảy ra khi tạo URL xác thực");
        }
    }

    public async Task<Result<string>> GetMinimalAuthorizationUrlAsync(string shopId, string clientId, string redirectUri, string state = "")
    {
        try
        {
            var config = await _sapoOmniConfigRepository.GetByShopId(shopId);
            if (config == null || string.IsNullOrEmpty(config.DomainApi))
            {
                return Result<string>.Failure("Chưa cấu hình domain API cho shop");
            }

            // Scope tối thiểu cho webhook cơ bản
            var minimalScopes = new List<string>
            {
                "read_products", "write_products",
                "read_orders", "write_orders",
                "read_customers", "write_customers"
            };

            var scopeString = string.Join(",", minimalScopes);

            var authUrl = $"{config.DomainApi}/admin/oauth/authorize" +
                         $"?client_id={Uri.EscapeDataString(clientId)}" +
                         $"&redirect_uri={Uri.EscapeDataString(redirectUri)}" +
                         $"&scope={Uri.EscapeDataString(scopeString)}" +
                         $"&response_type=code" +
                         $"&state={Uri.EscapeDataString(state)}";

            _logger.LogInformation("Generated minimal authorization URL for shop {ShopId}", shopId);
            return Result<string>.Success(authUrl);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating minimal authorization URL for shop {ShopId}", shopId);
            return Result<string>.Failure("Có lỗi xảy ra khi tạo URL xác thực tối thiểu");
        }
    }

    public async Task<Result<string>> GetCustomAuthorizationUrlAsync(string shopId, string clientId, string redirectUri, List<string> requiredScopes, string state = "")
    {
        try
        {
            var config = await _sapoOmniConfigRepository.GetByShopId(shopId);
            if (config == null || string.IsNullOrEmpty(config.DomainApi))
            {
                return Result<string>.Failure("Chưa cấu hình domain API cho shop");
            }

            if (!requiredScopes?.Any() == true)
            {
                return Result<string>.Failure("Cần cung cấp ít nhất một scope");
            }

            // Validate scopes
            var validScopes = GetAllValidScopes();
            var invalidScopes = requiredScopes.Where(s => !validScopes.Contains(s)).ToList();

            if (invalidScopes.Any())
            {
                _logger.LogWarning("Invalid scopes requested: {InvalidScopes}", string.Join(", ", invalidScopes));
                return Result<string>.Failure($"Scope không hợp lệ: {string.Join(", ", invalidScopes)}");
            }

            var scopeString = string.Join(",", requiredScopes);

            var authUrl = $"{config.DomainApi}/admin/oauth/authorize" +
                         $"?client_id={Uri.EscapeDataString(clientId)}" +
                         $"&redirect_uri={Uri.EscapeDataString(redirectUri)}" +
                         $"&scope={Uri.EscapeDataString(scopeString)}" +
                         $"&response_type=code" +
                         $"&state={Uri.EscapeDataString(state)}";

            _logger.LogInformation("Generated custom authorization URL for shop {ShopId} with scopes: {Scopes}",
                shopId, scopeString);
            return Result<string>.Success(authUrl);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating custom authorization URL for shop {ShopId}", shopId);
            return Result<string>.Failure("Có lỗi xảy ra khi tạo URL xác thực tùy chỉnh");
        }
    }

    public async Task<Result<string>> ExchangeCodeForAccessTokenAsync(string shopId, string code, string clientId, string clientSecret)
    {
        try
        {
            var config = await _sapoOmniConfigRepository.GetByShopId(shopId);
            if (config == null || string.IsNullOrEmpty(config.DomainApi))
            {
                return Result<string>.Failure("Chưa cấu hình domain API cho shop");
            }

            using var httpClient = _httpClientFactory.CreateClient();
            var tokenUrl = $"{config.DomainApi}/admin/oauth/access_token";

            var requestData = new
            {
                client_id = clientId,
                client_secret = clientSecret,
                code = code
            };

            var response = await httpClient.PostAsync(tokenUrl, CreateJsonContent(requestData));
            var responseContent = await response.Content.ReadAsStringAsync();

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError("Failed to exchange code for access token: {StatusCode} - {Response}",
                    response.StatusCode, responseContent);
                return Result<string>.Failure($"Lỗi xác thực: {response.StatusCode}");
            }

            var tokenResponse = JsonConvert.DeserializeObject<SapoOmniTokenResponse>(responseContent);
            if (tokenResponse == null)
            {
                _logger.LogError("Invalid token response: {Response}", responseContent);
                return Result<string>.Failure("Phản hồi token không hợp lệ");
            }

            if (!tokenResponse.IsSuccess)
            {
                _logger.LogError("Token exchange returned error: {Error} - {Description}",
                    tokenResponse.Error, tokenResponse.ErrorDescription);
                return Result<string>.Failure($"Lỗi OAuth: {tokenResponse.ErrorDescription ?? tokenResponse.Error}");
            }

            if (string.IsNullOrEmpty(tokenResponse.AccessToken))
            {
                return Result<string>.Failure("Access token không được trả về");
            }

            var updateResult = await UpdateAccessTokenAsync(config.DomainApi, shopId, tokenResponse.AccessToken);
            if (!updateResult)
            {
                _logger.LogWarning("Failed to update access token in database for shop {ShopId}", shopId);
            }

            _logger.LogInformation("Successfully obtained access token for shop {ShopId} with scopes: {Scopes}",
                shopId, tokenResponse.Scope);

            return Result<string>.Success(tokenResponse.AccessToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error exchanging code for access token for shop {ShopId}", shopId);
            return Result<string>.Failure("Có lỗi xảy ra khi lấy access token");
        }
    }

    public async Task<Result<bool>> ValidateAccessTokenAsync(string shopId)
    {
        try
        {
            var config = await _sapoOmniConfigRepository.GetByShopId(shopId);
            if (config == null || string.IsNullOrEmpty(config.AccessToken))
            {
                return Result<bool>.Failure("Không tìm thấy access token");
            }

            using var httpClient = CreateConfiguredHttpClient(config.AccessToken);
            var testUrl = $"{config.DomainApi}/admin/shop.json";

            var response = await httpClient.GetAsync(testUrl);

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Access token is valid for shop {ShopId}", shopId);
                return Result<bool>.Success(true);
            }
            else if (response.StatusCode == System.Net.HttpStatusCode.Unauthorized)
            {
                _logger.LogWarning("Access token is invalid or expired for shop {ShopId}", shopId);

                // Cập nhật status thành inactive khi token hết hạn
                await UpdateConfigStatusAsync(config.DomainApi, shopId, "Inactive");

                return Result<bool>.Failure("Access token không hợp lệ hoặc đã hết hạn");
            }
            else
            {
                _logger.LogError("Error validating access token for shop {ShopId}: {StatusCode}",
                    shopId, response.StatusCode);
                return Result<bool>.Failure($"Lỗi kiểm tra token: {response.StatusCode}");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating access token for shop {ShopId}", shopId);
            return Result<bool>.Failure("Có lỗi xảy ra khi kiểm tra access token");
        }
    }



    #endregion

    #region Webhook Management

    public async Task<Result<bool>> CreateWebhooksAsync(string shopId, string webhookUrl)
    {
        try
        {
            var config = await _sapoOmniConfigRepository.GetByShopId(shopId);
            if (config == null || string.IsNullOrEmpty(config.AccessToken))
            {
                return Result<bool>.Failure("Không tìm thấy cấu hình SapoOmni hoặc access token");
            }

            var webhookIds = await CreateWebHooksAsync(config.DomainApi, config.AccessToken, webhookUrl);

            if (webhookIds == null || webhookIds.Count == 0)
            {
                return Result<bool>.Failure("Không thể tạo webhook hoặc kết nối thất bại");
            }

            _logger.LogInformation("Successfully created {Count} webhooks for shop {ShopId}",
                webhookIds.Count, shopId);

            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating webhooks for shop {ShopId}", shopId);
            return Result<bool>.Failure("Có lỗi xảy ra khi tạo webhook");
        }
    }


    private async Task<List<long>> CreateWebHooksAsync(string domainApi, string accessToken, string webhookUrl)
    {
        try
        {
            var result = new List<long>();

            var apiUrlTemplate = $"https://{domainApi}/admin/webhooks.json";
            var permissions = GetDefaultPermissions();

            if (string.IsNullOrEmpty(apiUrlTemplate) || string.IsNullOrEmpty(webhookUrl))
            {
                _logger.LogError("SapoOmiConfig missing required settings: WebhookUrl or ApiUrl");
                return new List<long>();
            }

            if (permissions == null || permissions.Count == 0)
            {
                _logger.LogWarning("No permissions configured in SapoOmiConfig:Permissions");
                return new List<long>();
            }

            using var httpClient = _httpClientFactory.CreateClient();
            httpClient.Timeout = TimeSpan.FromSeconds(30);

            var apiUrl = string.Format(apiUrlTemplate, domainApi);
            httpClient.DefaultRequestHeaders.Clear();
            httpClient.DefaultRequestHeaders.Add("X-Sapo-Access-Token", accessToken);

            _logger.LogInformation("Creating webhooks for domain: {Domain} with {Count} permissions",
                domainApi, permissions.Count);

            foreach (var permission in permissions)
            {
                try
                {
                    var webhookData = new
                    {
                        webhook = new
                        {
                            topic = permission,
                            address = webhookUrl,
                            format = "json",
                            api_version = "2023-01"
                        }
                    };

                    var jsonContent = JsonConvert.SerializeObject(webhookData);
                    var content = new StringContent(jsonContent, Encoding.UTF8, "application/json");

                    _logger.LogInformation("Creating webhook for permission: {Permission}", permission);
                    var response = await httpClient.PostAsync(apiUrl, content);
                    var responseContent = await response.Content.ReadAsStringAsync();

                    if (response.IsSuccessStatusCode)
                    {
                        var jsonNode = JsonNode.Parse(responseContent);
                        if (jsonNode?["webhook"]?["id"] != null)
                        {
                            var webhookId = (long)jsonNode["webhook"]["id"];
                            result.Add(webhookId);
                            _logger.LogInformation("Created webhook for permission {Permission}: ID {WebhookId}",
                                permission, webhookId);
                        }
                    }
                    else
                    {
                        _logger.LogWarning("Failed to create webhook for permission {Permission}: {StatusCode} - {Response}",
                            permission, response.StatusCode, responseContent);

                        if (response.StatusCode == System.Net.HttpStatusCode.UnprocessableEntity &&
                            responseContent.Contains("already exists"))
                        {
                            var existingWebhookId = await FindExistingWebhook(httpClient, domainApi, accessToken, permission, webhookUrl);
                            if (existingWebhookId.HasValue)
                            {
                                result.Add(existingWebhookId.Value);
                                _logger.LogInformation("Found existing webhook for permission {Permission}: ID {WebhookId}",
                                    permission, existingWebhookId.Value);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error creating webhook for permission {Permission}", permission);
                }
            }

            var message = result.Count > 0
                ? $"Successfully created/found {result.Count} webhooks out of {permissions.Count} permissions"
                : "No webhooks created";

            _logger.LogInformation("Webhook creation completed: {Message}", message);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating webhooks for domain {Domain}", domainApi);
            return new List<long>();
        }
    }

    /// <summary>
    /// Find existing webhook by topic and address
    /// </summary>
    private async Task<long?> FindExistingWebhook(HttpClient httpClient, string domainApi, string accessToken, string topic, string webhookUrl)
    {
        try
        {
            var listUrl = $"https://{domainApi}/admin/webhooks.json";
            var response = await httpClient.GetAsync(listUrl);

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var jsonNode = JsonNode.Parse(content);
                var webhooks = jsonNode?["webhooks"]?.AsArray();

                if (webhooks != null)
                {
                    foreach (var webhook in webhooks)
                    {
                        var webhookTopic = webhook?["topic"]?.ToString();
                        var webhookAddress = webhook?["address"]?.ToString();
                        var webhookIdNode = webhook?["id"];

                        if (webhookTopic == topic && webhookAddress == webhookUrl && webhookIdNode != null && webhookIdNode is JsonValue webhookIdValue)
                        {
                            return webhookIdValue.GetValue<long>();
                        }
                    }
                }
            }
            else
            {
                _logger.LogWarning("Failed to list existing webhooks: {StatusCode}", response.StatusCode);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error finding existing webhook for topic {Topic}", topic);
        }

        return null;
    }

    public async Task<Result<bool>> DeleteWebhooksAsync(string shopId)
    {
        try
        {
            var config = await _sapoOmniConfigRepository.GetByShopId(shopId);
            if (config == null || string.IsNullOrEmpty(config.AccessToken))
            {
                return Result<bool>.Failure("Không tìm thấy cấu hình SapoOmni hoặc access token");
            }

            using var httpClient = _httpClientFactory.CreateClient();
            httpClient.DefaultRequestHeaders.Clear();
            httpClient.DefaultRequestHeaders.Add("X-Sapo-Access-Token", config.AccessToken);

            // Lấy danh sách webhook hiện tại
            var getResponse = await httpClient.GetAsync($"https://{config.DomainApi}/admin/webhooks.json");
            if (!getResponse.IsSuccessStatusCode)
            {
                _logger.LogWarning("Failed to get existing webhooks for shop {ShopId}: {StatusCode}",
                    shopId, getResponse.StatusCode);
                return Result<bool>.Failure("Không thể lấy danh sách webhook hiện tại");
            }

            var webhooksContent = await getResponse.Content.ReadAsStringAsync();
            var jsonNode = JsonNode.Parse(webhooksContent);
            var webhooks = jsonNode?["webhooks"]?.AsArray();

            if (webhooks == null || !webhooks.Any())
            {
                _logger.LogInformation("No existing webhooks found for shop {ShopId}", shopId);
                return Result<bool>.Success(true);
            }

            var deletedCount = 0;
            var failedCount = 0;

            foreach (var webhook in webhooks)
            {
                try
                {
                    var webhookId = webhook?["id"]?.AsValue();
                    if (webhookId == null) continue;

                    var deleteResponse = await httpClient.DeleteAsync(
                        $"https://{config.DomainApi}/admin/webhooks/{webhookId}.json");

                    if (deleteResponse.IsSuccessStatusCode)
                    {
                        deletedCount++;
                        _logger.LogInformation("Deleted webhook {WebhookId} for shop {ShopId}", webhookId, shopId);
                    }
                    else
                    {
                        failedCount++;
                        _logger.LogWarning("Failed to delete webhook {WebhookId}: {StatusCode}",
                            webhookId, deleteResponse.StatusCode);
                    }

                    // Delay để tránh rate limit
                    await Task.Delay(100);
                }
                catch (Exception ex)
                {
                    failedCount++;
                    _logger.LogError(ex, "Error deleting webhook");
                }
            }

            _logger.LogInformation("Webhook deletion completed for shop {ShopId}. Deleted: {Deleted}, Failed: {Failed}",
                shopId, deletedCount, failedCount);

            return Result<bool>.Success(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting webhooks for shop {ShopId}", shopId);
            return Result<bool>.Failure("Có lỗi xảy ra khi xóa webhook");
        }
    }

    public async Task<Result<List<SapoOmniWebhookInfo>>> GetWebhooksAsync(string shopId)
    {
        try
        {
            var config = await _sapoOmniConfigRepository.GetByShopId(shopId);
            if (config == null || string.IsNullOrEmpty(config.AccessToken))
            {
                return Result<List<SapoOmniWebhookInfo>>.Failure("Không tìm thấy cấu hình SapoOmni hoặc access token");
            }

            using var httpClient = _httpClientFactory.CreateClient();
            httpClient.DefaultRequestHeaders.Clear();
            httpClient.DefaultRequestHeaders.Add("X-Sapo-Access-Token", config.AccessToken);

            var response = await httpClient.GetAsync($"https://{config.DomainApi}/admin/webhooks.json");

            if (!response.IsSuccessStatusCode)
            {
                return Result<List<SapoOmniWebhookInfo>>.Failure($"Lỗi lấy danh sách webhook: {response.StatusCode}");
            }

            var content = await response.Content.ReadAsStringAsync();
            var jsonNode = JsonNode.Parse(content);
            var webhooks = jsonNode?["webhooks"]?.AsArray();

            var webhookInfos = new List<SapoOmniWebhookInfo>();

            if (webhooks != null)
            {
                foreach (var webhook in webhooks)
                {
                    var webhookInfo = new SapoOmniWebhookInfo
                    {
                        Topic = webhook?["topic"]?.ToString() ?? "",
                        Address = webhook?["address"]?.ToString() ?? "",
                        Format = webhook?["format"]?.ToString() ?? "",
                        CreatedAt = DateTime.TryParse(webhook?["created_at"]?.ToString(), out var createdAt) ? createdAt : null,
                        UpdatedAt = DateTime.TryParse(webhook?["updated_at"]?.ToString(), out var updatedAt) ? updatedAt : null
                    };
                    webhookInfos.Add(webhookInfo);
                }
            }

            return Result<List<SapoOmniWebhookInfo>>.Success(webhookInfos);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting webhooks for shop {ShopId}", shopId);
            return Result<List<SapoOmniWebhookInfo>>.Failure("Có lỗi xảy ra khi lấy danh sách webhook");
        }
    }

    private string GetDefaultWebhookUrl()
    {
        string domainWildcard = _configuration["ApplicationSettings:DomainWildcard"] ?? @"localhost";

        return domainWildcard + "/api/webhook/ThirdPartySapoOmni";
    }
    private List<string> GetDefaultPermissions()
    {
        return new List<string>
        {
            "products/create",
            "products/update",
            "products/delete",
            "orders/create",
            "orders/update",
            "orders/delete",
            "customers/create",
            "customers/update",
            "customers/delete"
        };
    }
    private List<string> GetAllValidScopes()
    {
        return new List<string>
    {
        "read_products",
        "write_products",
        "read_customers",
        "write_customers",
        "read_suppliers",
        "write_suppliers",
        "read_orders",
        "write_orders",
        "read_fulfillments",
        "write_fulfillments",
        "read_order_returns",
        "write_order_returns",
        "read_stock_adjustments",
        "write_stock_adjustments",
        "read_stock_transfers",
        "write_stock_transfers",
        "read_purchase_orders",
        "write_purchase_orders"
    };
    }
    #endregion
}